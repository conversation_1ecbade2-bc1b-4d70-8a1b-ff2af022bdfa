
getgenv().H4x = getgenv().H4x or {}
H4x.Player = game:GetService("Players").LocalPlayer
H4x.ReplicatedStorage = game:GetService("ReplicatedStorage")
H4x.KillAura = false
H4x.BringingGunsArmor = false
H4x.BringingCrafting = false
H4x.BringingFuel = false
H4x.BringingFood = false
H4x.BringingHealing = false
H4x.SendingLogs = false
H4x.CraftingVisualsOn = false
H4x.ChildVisualsEnabled = false
H4x.ChestVisualsEnabled = false
H4x.GunsArmorESPActive = false
H4x.FuelESPActive = false
H4x.FoodESPActive = false
H4x.CraftingESPActive = false
H4x.AutoFarmTree = false
H4x.InfiniteJump = false
H4x.NoClip = false
H4x.Flying = false
H4x.FlySpeed = 50
H4x.WalkSpeedValue = 16
H4x.CraftingSelected = {}
H4x.SelectedGunsArmor = {}
H4x.SelectedCraftingItems = {}
H4x.SelectedFuel = {}
H4x.SelectedFoods = {}
H4x.SelectedHealing = {}
H4x.SelectedChest = nil
H4x.SelectedChild = nil
H4x.SelectedGunsArmorESP = "ALL"
H4x.SelectedFuelESP = "ALL"
H4x.SelectedFoodESP = "ALL"
H4x.SelectedCraftingESP = "ALL"

H4x.ActiveConnections = {}
H4x.ActiveThreads = {}

local MAX_KILL_DISTANCE = 125
local TARGET_CAMPFIRE_CFRAME = CFrame.new(15, 4, -30)
local TARGET_CAMPFIRE_POS = TARGET_CAMPFIRE_CFRAME.Position
local MAX_DISTANCE_TO_CAMPFIRE = 25
local IGNORE_CENTER = Vector3.new(0, 6, 0)
local IGNORE_RADIUS = 40
local GUNS_ARMOR_ITEMS = { "Revolver", "Rifle", "Revolver Ammo", "Rifle Ammo", "Leather Body", "Iron Body", "Thorn Body" }
local CRAFTING_ITEMS = { "Bolt", "Sheet Metal", "Old Radio", "Broken Fan", "Broken Microwave" }
local FUEL_ITEMS = { "Log", "Chair", "Coal", "Fuel Canister", "Oil Barrel", "Biofuel" }
local FOOD_ITEMS = { "Carrot", "Berry", "Morsel", "Steak", "Cooked Morsel", "Cooked Steak" }
local HEALING_ITEMS = { "Bandage", "MedKit" }

local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")


local workspaceItems = workspace:FindFirstChild("Items")
local workspaceCharacters = workspace:FindFirstChild("Characters")


local function cleanupConnections()
    for _, connection in pairs(H4x.ActiveConnections) do
        if connection and typeof(connection) == "RBXScriptConnection" then
            connection:Disconnect()
        end
    end
    H4x.ActiveConnections = {}
    
    for _, thread in pairs(H4x.ActiveThreads) do
        if thread then
            task.cancel(thread)
        end
    end
    H4x.ActiveThreads = {}
end

local function startThread(name, func)
    if H4x.ActiveThreads[name] then
        task.cancel(H4x.ActiveThreads[name])
    end
    H4x.ActiveThreads[name] = task.spawn(func)
end

local itemCache = {}
local lastItemScan = 0
local ITEM_SCAN_INTERVAL = 2 

local function getCachedItems()
    local currentTime = tick()
    if currentTime - lastItemScan > ITEM_SCAN_INTERVAL then
        workspaceItems = workspace:FindFirstChild("Items")
        if workspaceItems then
            itemCache = workspaceItems:GetChildren()
        else
            itemCache = {}
        end
        lastItemScan = currentTime
    end
    return itemCache
end

local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/download/1.6.0/main.lua"))()

local Window = WindUI:CreateWindow({
    Title = "Pulse Hub",
    Author = "Wow  sus",
    Folder = "PulseHub",
    Size = UDim2.fromOffset(580, 400),
    Transparent = true,
    Theme = "Dark",
    SideBarWidth = 140,
    Background = "",
    User = {
        Enabled = false,
        Anonymous = true,
        Callback = function()
        end
    }
})

local MainTab = Window:Tab({
    Title = "Main",
    Icon = "code",
    Locked = false
})
local ResourceTab = Window:Tab({
    Title = "Resources",
    Icon = "box",
    Locked = false
})
local VisualsTab = Window:Tab({
    Title = "Visuals",
    Icon = "badge-alert",
    Locked = false
})
local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "list",
    Locked = false
})
local PlayersTab = Window:Tab({
    Title = "Players",
    Icon = "user",
    Locked = false
})

Window:SelectTab(1)

local function createBillboardGui(text)
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 100, 0, 30)
    billboard.Adornee = nil
    billboard.AlwaysOnTop = true
    local label = Instance.new("TextLabel", billboard)
    label.BackgroundTransparency = 1
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextStrokeTransparency = 0
    label.Size = UDim2.new(1, 0, 1, 0)
    label.Font = Enum.Font.SourceSansBold
    label.TextScaled = true
    label.Text = text
    return billboard
end

local function clearVisuals()
    for _, v in pairs(H4x.VisualsFolder:GetChildren()) do
        v:Destroy()
    end
end



local childVisualsCache = {}
local function updateChildVisuals(state)
    if not state then
        for _, v in pairs(H4x.VisualsFolder:GetChildren()) do
            if v:IsA("Highlight") or v:IsA("BillboardGui") then
                v:Destroy()
            end
        end
        childVisualsCache = {}
        return
    end
    
    startThread("ChildESP", function()
        while H4x.ChildVisualsEnabled do
            local hrp = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
            if not hrp then task.wait(1) continue end
            
            local characters = workspaceCharacters:GetChildren()
            local currentChildren = {}
            
            for _, model in ipairs(characters) do
                if not H4x.ChildVisualsEnabled then break end
                
                if model:IsA("Model") and model.Name:match("^Lost Child") and model.PrimaryPart then
                    currentChildren[model] = true
                    local dist = (model.PrimaryPart.Position - hrp.Position).Magnitude
                    local hl = childVisualsCache[model] and childVisualsCache[model].highlight
                    
                    if not hl then
                        hl = Instance.new("Highlight")
                        hl.Name = model.Name .. "_Highlight"
                        hl.Adornee = model
                        hl.FillColor = Color3.fromRGB(255, 153, 0)
                        hl.OutlineColor = Color3.fromRGB(255, 153, 0)
                        hl.Parent = H4x.VisualsFolder
                        childVisualsCache[model] = childVisualsCache[model] or {}
                        childVisualsCache[model].highlight = hl
                    end
                    
                    local bb = childVisualsCache[model] and childVisualsCache[model].billboard
                    if not bb then
                        bb = createBillboardGui(model.Name)
                        bb.Adornee = model.PrimaryPart
                        bb.Parent = H4x.VisualsFolder
                        childVisualsCache[model].billboard = bb
                    end
                end
            end
            
            for model, visuals in pairs(childVisualsCache) do
                if not currentChildren[model] then
                    if visuals.highlight then visuals.highlight:Destroy() end
                    if visuals.billboard then visuals.billboard:Destroy() end
                    childVisualsCache[model] = nil
                end
            end
            
            task.wait(2)
        end
    end)
end

local chestVisualsCache = {}
local function updateChestVisuals(state)
    if not state then
        for _, v in ipairs(H4x.ChestESPFolder:GetChildren()) do
            if v:IsA("Highlight") or v:IsA("BillboardGui") then
                v:Destroy()
            end
        end
        chestVisualsCache = {}
        return
    end
    
    startThread("ChestESP", function()
        while H4x.ChestVisualsEnabled do
            local hrp = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
            if not hrp then task.wait(1) continue end
            
            local items = getCachedItems() 
            local currentChests = {}
            
            for _, model in ipairs(items) do
                if not H4x.ChestVisualsEnabled then break end
                
                if model:IsA("Model") and model.PrimaryPart and model.Name:match("^Item Chest") then
                    local opened = false
                    for attr, value in pairs(model:GetAttributes()) do
                        if type(attr) == "string" and attr:match("%d+Opened$") and value == true then
                            opened = true
                            break
                        end
                    end
                    
                    if not opened then
                        currentChests[model] = true
                        local dist = (model.PrimaryPart.Position - hrp.Position).Magnitude
                        local hl = chestVisualsCache[model] and chestVisualsCache[model].highlight
                        
                        if not hl then
                            hl = Instance.new("Highlight")
                            hl.Adornee = model
                            hl.FillColor = Color3.fromRGB(255, 153, 0)
                            hl.OutlineColor = Color3.fromRGB(255, 153, 0)
                            hl.Parent = H4x.ChestESPFolder
                            chestVisualsCache[model] = chestVisualsCache[model] or {}
                            chestVisualsCache[model].highlight = hl
                        end
                        
                        local bb = chestVisualsCache[model] and chestVisualsCache[model].billboard
                        if not bb then
                            bb = Instance.new("BillboardGui")
                            bb.Size = UDim2.new(0, 100, 0, 40)
                            bb.Adornee = model.PrimaryPart
                            bb.AlwaysOnTop = true
                            bb.StudsOffset = Vector3.new(0, 3, 0)
                            bb.Parent = H4x.ChestESPFolder
                            
                            local nameLabel = Instance.new("TextLabel", bb)
                            nameLabel.Size = UDim2.new(1, 0, 0.5, 0)
                            nameLabel.Position = UDim2.new(0, 0, 0, 0)
                            nameLabel.BackgroundTransparency = 1
                            nameLabel.Text = model.Name
                            nameLabel.TextColor3 = Color3.new(1, 1, 1)
                            nameLabel.TextStrokeTransparency = 0
                            nameLabel.Font = Enum.Font.SourceSansBold
                            nameLabel.TextScaled = true
                            
                            local distLabel = Instance.new("TextLabel", bb)
                            distLabel.Size = UDim2.new(1, 0, 0.5, 0)
                            distLabel.Position = UDim2.new(0, 0, 0.5, 0)
                            distLabel.BackgroundTransparency = 1
                            distLabel.Text = "[" .. math.floor(dist) .. "m]"
                            distLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
                            distLabel.TextStrokeTransparency = 0.4
                            distLabel.Font = Enum.Font.SourceSans
                            distLabel.TextScaled = true
                            
                            chestVisualsCache[model].billboard = bb
                            chestVisualsCache[model].distLabel = distLabel
                        else
                            chestVisualsCache[model].distLabel.Text = "[" .. math.floor(dist) .. "m]"
                        end
                    end
                end
            end
            
            for model, visuals in pairs(chestVisualsCache) do
                if not currentChests[model] then
                    if visuals.highlight then visuals.highlight:Destroy() end
                    if visuals.billboard then visuals.billboard:Destroy() end
                    chestVisualsCache[model] = nil
                end
            end
            
            task.wait(3)
        end
    end)
end

local function isChestOpened(model)
    for attr, value in pairs(model:GetAttributes()) do
        if type(attr) == "string" and attr:match("%d+Opened$") and value == true then
            return true
        end
    end
    return false
end

-- Specialized ESP Functions
local function createESPBillboard(itemName, distance, color)
    local bb = Instance.new("BillboardGui")
    bb.Size = UDim2.new(0, 120, 0, 50)
    bb.AlwaysOnTop = true
    bb.StudsOffset = Vector3.new(0, 3, 0)

    local nameLabel = Instance.new("TextLabel", bb)
    nameLabel.Size = UDim2.new(1, 0, 0.6, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = itemName
    nameLabel.TextColor3 = color or Color3.new(1, 1, 1)
    nameLabel.TextStrokeTransparency = 0
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextScaled = true

    local distLabel = Instance.new("TextLabel", bb)
    distLabel.Size = UDim2.new(1, 0, 0.4, 0)
    distLabel.Position = UDim2.new(0, 0, 0.6, 0)
    distLabel.BackgroundTransparency = 1
    distLabel.Text = "[" .. math.floor(distance) .. "m]"
    distLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    distLabel.TextStrokeTransparency = 0.3
    distLabel.Font = Enum.Font.SourceSans
    distLabel.TextScaled = true

    return bb, nameLabel, distLabel
end

local function clearESPHighlights(highlightTable, folder)
    for item, data in pairs(highlightTable) do
        pcall(function()
            if data.highlight then data.highlight:Destroy() end
            if data.billboard then data.billboard:Destroy() end
        end)
    end
    table.clear(highlightTable)
    if folder then
        for _, child in pairs(folder:GetChildren()) do
            if child:IsA("Highlight") or child:IsA("BillboardGui") then
                child:Destroy()
            end
        end
    end
end

local function updateSpecializedESP(itemArray, selectedItem, highlightTable, folder, color, espName)
    if not H4x[espName .. "Active"] then return end

    local hrp = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
    if not hrp then return end

    local currentItems = {}
    local items = getCachedItems()

    for _, item in pairs(items) do
        if item:IsA("Model") and item.Name and item.Name ~= "" and item.PrimaryPart then
            local itemName = tostring(item.Name)

            -- Check if item is in the category array
            local isInCategory = false
            for _, categoryItem in ipairs(itemArray) do
                if itemName == categoryItem then
                    isInCategory = true
                    break
                end
            end

            if isInCategory and (selectedItem == "ALL" or itemName == selectedItem) then
                local distance = (item.PrimaryPart.Position - hrp.Position).Magnitude

                if distance <= 500 then -- Max render distance
                    currentItems[item] = true

                    if not highlightTable[item] then
                        local highlight = Instance.new("Highlight")
                        highlight.Adornee = item
                        highlight.FillColor = color
                        highlight.OutlineColor = color
                        highlight.FillTransparency = 0.6
                        highlight.OutlineTransparency = 0
                        highlight.Parent = folder

                        local billboard, nameLabel, distLabel = createESPBillboard(itemName, distance, color)
                        billboard.Adornee = item.PrimaryPart
                        billboard.Parent = folder

                        highlightTable[item] = {
                            highlight = highlight,
                            billboard = billboard,
                            nameLabel = nameLabel,
                            distLabel = distLabel
                        }
                    end

                    -- Update distance
                    if highlightTable[item].distLabel then
                        highlightTable[item].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                    end
                end
            end
        end
    end

    -- Clean up items that are no longer valid
    for item, data in pairs(highlightTable) do
        if not currentItems[item] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            highlightTable[item] = nil
        end
    end
end

-- Individual ESP Update Functions
local function updateGunsArmorESP()
    updateSpecializedESP(GUNS_ARMOR_ITEMS, H4x.SelectedGunsArmorESP, H4x.GunsArmorHighlights, H4x.GunsArmorESPFolder, Color3.fromRGB(255, 0, 0), "GunsArmorESP")
end

local function updateFuelESP()
    updateSpecializedESP(FUEL_ITEMS, H4x.SelectedFuelESP, H4x.FuelHighlights, H4x.FuelESPFolder, Color3.fromRGB(255, 165, 0), "FuelESP")
end

local function updateFoodESP()
    updateSpecializedESP(FOOD_ITEMS, H4x.SelectedFoodESP, H4x.FoodHighlights, H4x.FoodESPFolder, Color3.fromRGB(0, 255, 0), "FoodESP")
end

local function updateCraftingESP()
    updateSpecializedESP(CRAFTING_ITEMS, H4x.SelectedCraftingESP, H4x.CraftingHighlights, H4x.CraftingESPFolder, Color3.fromRGB(0, 100, 255), "CraftingESP")
end

local function setupInfiniteJump()
    UserInputService.JumpRequest:Connect(function()
        if H4x.InfiniteJump and H4x.Player.Character and H4x.Player.Character:FindFirstChild("Humanoid") then
            H4x.Player.Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
        end
    end)
end

local function updateNoClip(state)
    local character = H4x.Player.Character
    if character then
        for _, part in pairs(character:GetDescendants()) do
            if part:IsA("BasePart") then
                part.CanCollide = not state
            end
        end
    end
    if state then
        spawn(function()
            while H4x.NoClip do
                character = H4x.Player.Character
                if character then
                    for _, part in pairs(character:GetDescendants()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end
                wait()
            end
        end)
    end
end

local function setupFly()
    local character = H4x.Player.Character
    local humanoidRootPart = character and character:FindFirstChild("HumanoidRootPart")
    local humanoid = character and character:FindFirstChild("Humanoid")
    if not character or not humanoidRootPart or not humanoid then return end

    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(math.huge, math.huge, math.huge)
    bodyVelocity.Velocity = Vector3.new(0, 0, 0)
    bodyVelocity.Parent = humanoidRootPart

    local bodyGyro = Instance.new("BodyGyro")
    bodyGyro.MaxTorque = Vector3.new(math.huge, math.huge, math.huge)
    bodyGyro.P = 10000
    bodyGyro.D = 1000
    bodyGyro.Parent = humanoidRootPart

    local camera = workspace.CurrentCamera
    local flyConnection

    flyConnection = RunService.RenderStepped:Connect(function(delta)
        if not H4x.Flying or not H4x.Player.Character or not H4x.Player.Character:FindFirstChild("HumanoidRootPart") then
            bodyVelocity:Destroy()
            bodyGyro:Destroy()
            flyConnection:Disconnect()
            return
        end

        local moveDirection = Vector3.new(0, 0, 0)
        if UserInputService:IsKeyDown(Enum.KeyCode.W) then
            moveDirection = moveDirection + camera.CFrame.LookVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.S) then
            moveDirection = moveDirection - camera.CFrame.LookVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.A) then
            moveDirection = moveDirection - camera.CFrame.RightVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.D) then
            moveDirection = moveDirection + camera.CFrame.RightVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
            moveDirection = moveDirection + Vector3.new(0, 1, 0)
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
            moveDirection = moveDirection - Vector3.new(0, 1, 0)
        end

        if moveDirection.Magnitude > 0 then
            moveDirection = moveDirection.Unit * H4x.FlySpeed
        end
        bodyVelocity.Velocity = moveDirection
        bodyGyro.CFrame = camera.CFrame
    end)
end

MainTab:Section({ 
    Title = "Main",
    TextXAlignment = "Left",
    TextSize = 17
})

MainTab:Toggle({
    Title = "Kill Aura",
    Default = false,
    Callback = function(state)
        H4x.KillAura = state
        if H4x.KillAura then
            startThread("KillAura", function()
                while H4x.KillAura do
                    local hrp = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if not hrp then task.wait(1) continue end
                    
                    local success, toolName = pcall(function()
                        local toolHandle = H4x.Player.Character:FindFirstChild("ToolHandle")
                        if toolHandle and toolHandle:FindFirstChild("OriginalItem") then
                            local toolNameAttr = toolHandle:GetAttribute("ToolName")
                            if toolNameAttr == "GenericAxe" or toolNameAttr == "GenericSword" then
                                return toolHandle.OriginalItem.Value
                            end
                        end
                        return nil
                    end)
                    
                    if success and toolName then
                        local characters = workspaceCharacters:GetChildren()
                        for _, model in ipairs(characters) do
                            if not H4x.KillAura then break end
                            if model:IsA("Model") and model.PrimaryPart then
                                local dist = (model.PrimaryPart.Position - hrp.Position).Magnitude
                                if dist <= MAX_KILL_DISTANCE then
                                    pcall(function()
                                        H4x.ReplicatedStorage.RemoteEvents.ToolDamageObject:InvokeServer(
                                            model,
                                            toolName,
                                            "1_5221910054",
                                            model.PrimaryPart.CFrame
                                        )
                                    end)
                                end
                            end
                        end
                    end
                    task.wait(0.5) 
                end
            end)
        else
            if H4x.ActiveThreads["KillAura"] then
                task.cancel(H4x.ActiveThreads["KillAura"])
                H4x.ActiveThreads["KillAura"] = nil
            end
        end
    end
})

MainTab:Toggle({
    Title = "Auto Farm Tree",
    Default = false,
    Callback = function(state)
        H4x.AutoFarmTree = state
        if H4x.AutoFarmTree then
            startThread("AutoFarmTree", function()
                while H4x.AutoFarmTree do
                    local hrp = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if not hrp then task.wait(1) continue end
                    
                    local success, toolName = pcall(function()
                        local toolHandle = H4x.Player.Character:FindFirstChild("ToolHandle")
                        if toolHandle and toolHandle:FindFirstChild("OriginalItem") then
                            local toolNameAttr = toolHandle:GetAttribute("ToolName")
                            if toolNameAttr == "GenericAxe" then
                                return toolHandle.OriginalItem.Value
                            end
                        end
                        return nil
                    end)
                    
                    if success and toolName then
                        local trees = {}
                        local foliage = workspace.Map.Foliage
                        local landmarks = workspace.Map.Landmarks
                        
                        if foliage then
                            for _, tree in pairs(foliage:GetChildren()) do
                                if not H4x.AutoFarmTree then break end
                                if tree.Name == "Small Tree" then
                                    local treePosition = tree:IsA("BasePart") and tree.Position or (tree:IsA("Model") and tree:GetPivot().Position)
                                    if treePosition then
                                        local distance = (hrp.Position - treePosition).Magnitude
                                        table.insert(trees, {tree = tree, distance = distance})
                                    end
                                end
                            end
                        end
                        
                        if landmarks then
                            for _, tree in pairs(landmarks:GetChildren()) do
                                if not H4x.AutoFarmTree then break end
                                if tree.Name == "Small Tree" then
                                    local treePosition = tree:IsA("BasePart") and tree.Position or (tree:IsA("Model") and tree:GetPivot().Position)
                                    if treePosition then
                                        local distance = (hrp.Position - treePosition).Magnitude
                                        table.insert(trees, {tree = tree, distance = distance})
                                    end
                                end
                            end
                        end
                        
                        if #trees == 0 then task.wait(1) continue end
                        table.sort(trees, function(a, b) return a.distance < b.distance end)
                        
                        for i = 1, math.min(3, #trees) do
                            if not H4x.AutoFarmTree then break end
                            local tree = trees[i].tree
                            pcall(function()
                                H4x.ReplicatedStorage.RemoteEvents.ToolDamageObject:InvokeServer(
                                    tree,
                                    toolName,
                                    "1",
                                    CFrame.new(1, 1, 1)
                                )
                            end)
                        end
                    end
                    task.wait(1)
                end
            end)
        else
            if H4x.ActiveThreads["AutoFarmTree"] then
                task.cancel(H4x.ActiveThreads["AutoFarmTree"])
                H4x.ActiveThreads["AutoFarmTree"] = nil
            end
        end
    end
})

MainTab:Toggle({
    Title = "Auto Fill Hunger",
    Default = false,
    Callback = function(state)
        H4x.AutoEat = state
        if H4x.AutoEat then
            startThread("AutoEat", function()
                local success, bar = pcall(function()
                    return H4x.Player.PlayerGui:FindFirstChild("Interface")
                        and H4x.Player.PlayerGui.Interface:FindFirstChild("StatBars")
                        and H4x.Player.PlayerGui.Interface.StatBars:FindFirstChild("HungerBar")
                        and H4x.Player.PlayerGui.Interface.StatBars.HungerBar:FindFirstChild("Bar")
                end)
                if not success or not bar then return end
                
                while H4x.AutoEat do
                    local scale = bar.Size.X.Scale
                    if scale < 0.9 then
                        local items = getCachedItems()
                        for _, itemName in ipairs({"Carrot", "Berry", "Cooked Morsel", "Cooked Steak"}) do
                            if not H4x.AutoEat then break end
                            for _, item in ipairs(items) do
                                if item.Name == itemName then
                                    pcall(function()
                                        H4x.ReplicatedStorage.RemoteEvents.RequestConsumeItem:InvokeServer(item)
                                    end)
                                    task.wait(0.2)
                                    break
                                end
                            end
                        end
                    end
                    task.wait(2) 
                end
            end)
        else
            if H4x.ActiveThreads["AutoEat"] then
                task.cancel(H4x.ActiveThreads["AutoEat"])
                H4x.ActiveThreads["AutoEat"] = nil
            end
        end
    end
})

MainTab:Section({ 
    Title = "Others",
    TextXAlignment = "Left",
    TextSize = 17
})

MainTab:Toggle({
    Title = "Insta Open Chest + Range",
    Default = false,
    Callback = function(state)
        H4x.InstaOpenChest = state
        if H4x.InstaOpenChest then
            spawn(function()
                while H4x.InstaOpenChest do
                    local items = workspace.Items:GetChildren()
                    local success, err = pcall(function()
                        for _, model in ipairs(items) do
                            if model:IsA("Model") and model.PrimaryPart and model.Name:match("^Item Chest") and not isChestOpened(model) then
                                local proximityAttachment = model:FindFirstChild("Main", true)
                                if proximityAttachment and proximityAttachment:FindFirstChild("ProximityAttachment") then
                                    local proximityInteraction = proximityAttachment.ProximityAttachment:FindFirstChild("ProximityInteraction")
                                    if proximityInteraction and proximityInteraction:IsA("ProximityPrompt") then
                                        proximityInteraction.HoldDuration = 0
                                        proximityInteraction.RequiresLineOfSight = false
                                        proximityInteraction.MaxActivationDistance = 40
                                    end
                                end
                            end
                        end
                    end)
                    wait(1)
                end
            end)
        else
            local items = workspace.Items:GetChildren()
            local success, err = pcall(function()
                for _, model in ipairs(items) do
                    if model:IsA("Model") and model.PrimaryPart and model.Name:match("^Item Chest") and not isChestOpened(model) then
                        local proximityAttachment = model:FindFirstChild("Main", true)
                        if proximityAttachment and proximityAttachment:FindFirstChild("ProximityAttachment") then
                            local proximityInteraction = proximityAttachment.ProximityAttachment:FindFirstChild("ProximityInteraction")
                            if proximityInteraction and proximityInteraction:IsA("ProximityPrompt") then
                                proximityInteraction.HoldDuration = 0.5
                                proximityInteraction.RequiresLineOfSight = true
                                proximityInteraction.MaxActivationDistance = 10
                            end
                        end
                    end
                end
            end)
        end
    end
})

MainTab:Toggle({
    Title = "TP to Fire When Night",
    Default = false,
    Callback = function(state)
        if state then
            spawn(function()
                local lastState = nil
                while state do
                    local current = workspace:GetAttribute("State")
                    if current == "Night" and lastState ~= "Night" then
                        local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                        local fire = workspace.Map.Campground:FindFirstChild("MainFire")
                        if root and fire and fire.PrimaryPart then
                            root.CFrame = CFrame.new(fire.PrimaryPart.Position + Vector3.new(0, 15, 0))
                        end
                    end
                    lastState = current
                    wait(1)
                end
            end)
        end
    end
})

MainTab:Toggle({
    Title = "Send Logs to CampFire",
    Default = false,
    Callback = function(state)
        H4x.SendingLogs = state
        if H4x.SendingLogs then
            spawn(function()
                while H4x.SendingLogs do
                    local items = workspace.Items:GetChildren()
                    for _, log in ipairs(items) do
                        if log:IsA("Model") and log.Name == "Log" and log.PrimaryPart then
                            local logPos = log.PrimaryPart.Position
                            local distToTarget = (logPos - TARGET_CAMPFIRE_POS).Magnitude
                            local distToIgnoreCenter = (logPos - IGNORE_CENTER).Magnitude
                            if distToTarget > MAX_DISTANCE_TO_CAMPFIRE and distToIgnoreCenter > IGNORE_RADIUS then
                                log:PivotTo(TARGET_CAMPFIRE_CFRAME)
                            end
                        end
                    end
                    wait(1)
                end
            end)
        end
    end
})

ResourceTab:Section({ 
    Title = "Guns and Armor",
    TextXAlignment = "Left",
    TextSize = 17
})

ResourceTab:Dropdown({
    Title = "Select Guns and Armor",
    Values = GUNS_ARMOR_ITEMS,
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selected)
        H4x.SelectedGunsArmor = selected
    end
})

ResourceTab:Toggle({
    Title = "Bring Guns and Armor",
    Default = false,
    Callback = function(state)
        H4x.BringingGunsArmor = state
        if H4x.BringingGunsArmor then
            spawn(function()
                while H4x.BringingGunsArmor do
                    local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if root and H4x.SelectedGunsArmor and #H4x.SelectedGunsArmor > 0 then
                        local items = workspace.Items:GetChildren()
                        local itemsMoved = 0
                        local maxItemsPerBatch = 10
                        for _, itemName in ipairs(H4x.SelectedGunsArmor) do
                            for _, item in ipairs(items) do
                                if itemsMoved >= maxItemsPerBatch then break end
                                if item:IsA("Model") and item.Name == itemName and item.PrimaryPart then
                                    local dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                    if dist > 30 then
                                        local attempts = 0
                                        local maxAttempts = 5
                                        while dist > 30 and attempts < maxAttempts and H4x.BringingGunsArmor do
                                            item:PivotTo(root.CFrame)
                                            wait(0.05)
                                            dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                            attempts = attempts + 1
                                        end
                                        itemsMoved = itemsMoved + 1
                                    end
                                end
                            end
                            if itemsMoved >= maxItemsPerBatch or not H4x.BringingGunsArmor then break end
                        end
                    end
                    wait(0.4)
                end
            end)
        end
    end
})

ResourceTab:Section({ 
    Title = "Crafting Items",
    TextXAlignment = "Left",
    TextSize = 17
})

ResourceTab:Dropdown({
    Title = "Select Crafting Items",
    Values = CRAFTING_ITEMS,
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selected)
        H4x.SelectedCraftingItems = selected
    end
})

ResourceTab:Toggle({
    Title = "Bring Crafting Items",
    Default = false,
    Callback = function(state)
        H4x.BringingCrafting = state
        if H4x.BringingCrafting then
            spawn(function()
                while H4x.BringingCrafting do
                    local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if root and H4x.SelectedCraftingItems and #H4x.SelectedCraftingItems > 0 then
                        local items = workspace.Items:GetChildren()
                        local itemsMoved = 0
                        local maxItemsPerBatch = 10
                        for _, itemName in ipairs(H4x.SelectedCraftingItems) do
                            for _, item in ipairs(items) do
                                if itemsMoved >= maxItemsPerBatch then break end
                                if item:IsA("Model") and item.Name == itemName and item.PrimaryPart then
                                    local dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                    if dist > 30 then
                                        local attempts = 0
                                        local maxAttempts = 5
                                        while dist > 30 and attempts < maxAttempts and H4x.BringingCrafting do
                                            item:PivotTo(root.CFrame)
                                            wait(0.05)
                                            dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                            attempts = attempts + 1
                                        end
                                        itemsMoved = itemsMoved + 1
                                    end
                                end
                            end
                            if itemsMoved >= maxItemsPerBatch or not H4x.BringingCrafting then break end
                        end
                    end
                    wait(0.4)
                end
            end)
        end
    end
})

ResourceTab:Section({ 
    Title = "Fuel Items",
    TextXAlignment = "Left",
    TextSize = 17
})

ResourceTab:Dropdown({
    Title = "Select Fuel Items",
    Values = FUEL_ITEMS,
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selected)
        H4x.SelectedFuel = selected
    end
})

ResourceTab:Toggle({
    Title = "Bring Fuel Items",
    Default = false,
    Callback = function(state)
        H4x.BringingFuel = state
        if H4x.BringingFuel then
            spawn(function()
                while H4x.BringingFuel do
                    local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if root and H4x.SelectedFuel and #H4x.SelectedFuel > 0 then
                        local items = workspace.Items:GetChildren()
                        local itemsMoved = 0
                        local maxItemsPerBatch = 10
                        for _, fuelName in ipairs(H4x.SelectedFuel) do
                            for _, item in ipairs(items) do
                                if itemsMoved >= maxItemsPerBatch then break end
                                if item:IsA("Model") and item.Name == fuelName and item.PrimaryPart then
                                    local dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                    if dist > 30 then
                                        local attempts = 0
                                        local maxAttempts = 5
                                        while dist > 30 and attempts < maxAttempts and H4x.BringingFuel do
                                            item:PivotTo(root.CFrame)
                                            wait(0.05)
                                            dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                            attempts = attempts + 1
                                        end
                                        itemsMoved = itemsMoved + 1
                                    end
                                end
                            end
                            if itemsMoved >= maxItemsPerBatch or not H4x.BringingFuel then break end
                        end
                    end
                    wait(0.4)
                end
            end)
        end
    end
})

ResourceTab:Section({ 
    Title = "Food Items",
    TextXAlignment = "Left",
    TextSize = 17
})

ResourceTab:Dropdown({
    Title = "Select Food Items",
    Values = FOOD_ITEMS,
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selected)
        H4x.SelectedFoods = selected
    end
})

ResourceTab:Toggle({
    Title = "Bring Food Items",
    Default = false,
    Callback = function(state)
        H4x.BringingFood = state
        if H4x.BringingFood then
            spawn(function()
                while H4x.BringingFood do
                    local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if root and H4x.SelectedFoods and #H4x.SelectedFoods > 0 then
                        local items = workspace.Items:GetChildren()
                        local itemsMoved = 0
                        local maxItemsPerBatch = 10
                        for _, foodName in ipairs(H4x.SelectedFoods) do
                            for _, item in ipairs(items) do
                                if itemsMoved >= maxItemsPerBatch then break end
                                if item:IsA("Model") and item.Name == foodName and item.PrimaryPart then
                                    local dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                    if dist > 30 then
                                        local attempts = 0
                                        local maxAttempts = 5
                                        while dist > 30 and attempts < maxAttempts and H4x.BringingFood do
                                            item:PivotTo(root.CFrame)
                                            wait(0.05)
                                            dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                            attempts = attempts + 1
                                        end
                                        itemsMoved = itemsMoved + 1
                                    end
                                end
                            end
                            if itemsMoved >= maxItemsPerBatch or not H4x.BringingFood then break end
                        end
                    end
                    wait(0.4)
                end
            end)
        end
    end
})

ResourceTab:Section({ 
    Title = "Healing Items",
    TextXAlignment = "Left",
    TextSize = 17
})

ResourceTab:Dropdown({
    Title = "Select Healing Items",
    Values = HEALING_ITEMS,
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selected)
        H4x.SelectedHealing = selected
    end
})

ResourceTab:Toggle({
    Title = "Bring Healing Items",
    Default = false,
    Callback = function(state)
        H4x.BringingHealing = state
        if H4x.BringingHealing then
            spawn(function()
                while H4x.BringingHealing do
                    local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
                    if root and H4x.SelectedHealing and #H4x.SelectedHealing > 0 then
                        local items = workspace.Items:GetChildren()
                        local itemsMoved = 0
                        local maxItemsPerBatch = 10
                        for _, healingName in ipairs(H4x.SelectedHealing) do
                            for _, item in ipairs(items) do
                                if itemsMoved >= maxItemsPerBatch then break end
                                if item:IsA("Model") and item.Name == healingName and item.PrimaryPart then
                                    local dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                    if dist > 30 then
                                        local attempts = 0
                                        local maxAttempts = 5
                                        while dist > 30 and attempts < maxAttempts and H4x.BringingHealing do
                                            item:PivotTo(root.CFrame)
                                            wait(0.05)
                                            dist = (item.PrimaryPart.Position - root.Position).Magnitude
                                            attempts = attempts + 1
                                        end
                                        itemsMoved = itemsMoved + 1
                                    end
                                end
                            end
                            if itemsMoved >= maxItemsPerBatch or not H4x.BringingHealing then break end
                        end
                    end
                    wait(0.4)
                end
            end)
        end
    end
})

H4x.VisualsFolder = Instance.new("Folder", workspace)
H4x.VisualsFolder.Name = "ItemVisuals"
H4x.ChestESPFolder = Instance.new("Folder", workspace)
H4x.ChestESPFolder.Name = "ChestESP"

-- ESP Folders for specialized categories
H4x.GunsArmorESPFolder = Instance.new("Folder", workspace)
H4x.GunsArmorESPFolder.Name = "GunsArmorESP"
H4x.FuelESPFolder = Instance.new("Folder", workspace)
H4x.FuelESPFolder.Name = "FuelESP"
H4x.FoodESPFolder = Instance.new("Folder", workspace)
H4x.FoodESPFolder.Name = "FoodESP"
H4x.CraftingESPFolder = Instance.new("Folder", workspace)
H4x.CraftingESPFolder.Name = "CraftingESP"

-- ESP Highlight Storage
H4x.GunsArmorHighlights = {}
H4x.FuelHighlights = {}
H4x.FoodHighlights = {}
H4x.CraftingHighlights = {}

VisualsTab:Section({
    Title = "Specialized Item ESP",
    TextXAlignment = "Left",
    TextSize = 17
})

-- Gun & Armor ESP Section
VisualsTab:Dropdown({
    Title = "Gun & Armor ESP Select",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(GUNS_ARMOR_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(selected)
        H4x.SelectedGunsArmorESP = selected
        if H4x.GunsArmorESPActive then
            updateGunsArmorESP()
        end
    end
})

VisualsTab:Toggle({
    Title = "Gun & Armor ESP",
    Desc = "Highlight guns and armor items (Red)",
    Default = false,
    Callback = function(state)
        H4x.GunsArmorESPActive = state
        if state then
            startThread("GunsArmorESP", function()
                while H4x.GunsArmorESPActive do
                    pcall(updateGunsArmorESP)
                    task.wait(2)
                end
            end)
        else
            if H4x.ActiveThreads["GunsArmorESP"] then
                task.cancel(H4x.ActiveThreads["GunsArmorESP"])
                H4x.ActiveThreads["GunsArmorESP"] = nil
            end
            clearESPHighlights(H4x.GunsArmorHighlights, H4x.GunsArmorESPFolder)
        end
    end
})

-- Fuel ESP Section
VisualsTab:Dropdown({
    Title = "Fuel ESP Select",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FUEL_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(selected)
        H4x.SelectedFuelESP = selected
        if H4x.FuelESPActive then
            updateFuelESP()
        end
    end
})

VisualsTab:Toggle({
    Title = "Fuel ESP",
    Desc = "Highlight fuel items (Orange)",
    Default = false,
    Callback = function(state)
        H4x.FuelESPActive = state
        if state then
            startThread("FuelESP", function()
                while H4x.FuelESPActive do
                    pcall(updateFuelESP)
                    task.wait(2)
                end
            end)
        else
            if H4x.ActiveThreads["FuelESP"] then
                task.cancel(H4x.ActiveThreads["FuelESP"])
                H4x.ActiveThreads["FuelESP"] = nil
            end
            clearESPHighlights(H4x.FuelHighlights, H4x.FuelESPFolder)
        end
    end
})

-- Food ESP Section
VisualsTab:Dropdown({
    Title = "Food ESP Select",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FOOD_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(selected)
        H4x.SelectedFoodESP = selected
        if H4x.FoodESPActive then
            updateFoodESP()
        end
    end
})

VisualsTab:Toggle({
    Title = "Food ESP",
    Desc = "Highlight food items (Green)",
    Default = false,
    Callback = function(state)
        H4x.FoodESPActive = state
        if state then
            startThread("FoodESP", function()
                while H4x.FoodESPActive do
                    pcall(updateFoodESP)
                    task.wait(2)
                end
            end)
        else
            if H4x.ActiveThreads["FoodESP"] then
                task.cancel(H4x.ActiveThreads["FoodESP"])
                H4x.ActiveThreads["FoodESP"] = nil
            end
            clearESPHighlights(H4x.FoodHighlights, H4x.FoodESPFolder)
        end
    end
})

-- Crafting ESP Section
VisualsTab:Dropdown({
    Title = "Crafting ESP Select",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(CRAFTING_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(selected)
        H4x.SelectedCraftingESP = selected
        if H4x.CraftingESPActive then
            updateCraftingESP()
        end
    end
})

VisualsTab:Toggle({
    Title = "Crafting ESP",
    Desc = "Highlight crafting items (Blue)",
    Default = false,
    Callback = function(state)
        H4x.CraftingESPActive = state
        if state then
            startThread("CraftingESP", function()
                while H4x.CraftingESPActive do
                    pcall(updateCraftingESP)
                    task.wait(2)
                end
            end)
        else
            if H4x.ActiveThreads["CraftingESP"] then
                task.cancel(H4x.ActiveThreads["CraftingESP"])
                H4x.ActiveThreads["CraftingESP"] = nil
            end
            clearESPHighlights(H4x.CraftingHighlights, H4x.CraftingESPFolder)
        end
    end
})

-- ESP Control Buttons
VisualsTab:Button({
    Title = "Clear All ESP",
    Desc = "Remove all ESP highlights",
    Callback = function()
        clearESPHighlights(H4x.GunsArmorHighlights, H4x.GunsArmorESPFolder)
        clearESPHighlights(H4x.FuelHighlights, H4x.FuelESPFolder)
        clearESPHighlights(H4x.FoodHighlights, H4x.FoodESPFolder)
        clearESPHighlights(H4x.CraftingHighlights, H4x.CraftingESPFolder)
        print("All ESP highlights cleared!")
    end
})

VisualsTab:Button({
    Title = "Refresh Item Cache",
    Desc = "Update item detection for ESP",
    Callback = function()
        itemCache = {}
        lastItemScan = 0
        print("Item cache refreshed for ESP!")
    end
})

VisualsTab:Section({
    Title = "Other Visuals",
    TextXAlignment = "Left",
    TextSize = 17
})

VisualsTab:Toggle({
    Title = "Lost Child Visuals",
    Default = false,
    Callback = function(state)
        H4x.ChildVisualsEnabled = state
        if state then
            updateChildVisuals(state)
        else
            updateChildVisuals(state)
            if H4x.ActiveThreads["ChildESP"] then
                task.cancel(H4x.ActiveThreads["ChildESP"])
                H4x.ActiveThreads["ChildESP"] = nil
            end
        end
    end
})

VisualsTab:Toggle({
    Title = "Chest ESP (Unopened)",
    Default = false,
    Callback = function(state)
        H4x.ChestVisualsEnabled = state
        if state then
            updateChestVisuals(state)
        else
            updateChestVisuals(state)
            if H4x.ActiveThreads["ChestESP"] then
                task.cancel(H4x.ActiveThreads["ChestESP"])
                H4x.ActiveThreads["ChestESP"] = nil
            end
        end
    end
})

TeleportTab:Section({
    Title = "Chest Teleport",
    TextXAlignment = "Left",
    TextSize = 17
})

local chestDropdown
local chestMap = {}
local function updateChestDropdown()
    local chestList = {}
    chestMap = {}
    local index = 1
    local items = workspace.Items:GetChildren()
    for _, model in ipairs(items) do
        if model:IsA("Model") and model.Name:match("^Item Chest") and model.PrimaryPart and not isChestOpened(model) then
            local displayName = string.format("Chest %d", index)
            table.insert(chestList, displayName)
            chestMap[displayName] = model
            index = index + 1
        end
    end
    if #chestList == 0 then
        chestList = {"No unopened chests found"}
        chestMap = {}
    end
    chestDropdown:Refresh(chestList)
    if not chestMap[H4x.SelectedChest] and #chestList > 0 then
        H4x.SelectedChest = chestList[1]
    end
end

chestDropdown = TeleportTab:Dropdown({
    Title = "Select Chest",
    Values = {"Scanning for chests..."},
    Value = "Scanning for chests...",
    Multi = false,
    AllowNone = false,
    Callback = function(option)
        H4x.SelectedChest = option
    end
})

TeleportTab:Button({
    Title = "Teleport to Selected Chest",
    Description = "Teleports to the selected chest's location",
    Locked = false,
    Callback = function()
        if H4x.SelectedChest and H4x.SelectedChest ~= "No unopened chests found" and H4x.SelectedChest ~= "Scanning for chests..." then
            local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
            if root and chestMap[H4x.SelectedChest] and chestMap[H4x.SelectedChest].PrimaryPart then
                root.CFrame = CFrame.new(chestMap[H4x.SelectedChest].PrimaryPart.Position + Vector3.new(0, 5, 0))
            end
        end
    end
})

TeleportTab:Section({
    Title = "Lost Child Teleport",
    TextXAlignment = "Left",
    TextSize = 17
})

local childDropdown
local childMap = {}
local function updateChildDropdown()
    local childList = {}
    childMap = {}
    local index = 1
    local characters = workspaceCharacters:GetChildren() 
    for _, model in ipairs(characters) do
        if model:IsA("Model") and model.Name:match("^Lost Child") and model.PrimaryPart then
            local displayName = string.format("Child %d", index)
            table.insert(childList, displayName)
            childMap[displayName] = model
            index = index + 1
        end
    end
    if #childList == 0 then
        childList = {"No lost children found"}
        childMap = {}
    end
    if childDropdown then
        childDropdown:Refresh(childList)
        if not childMap[H4x.SelectedChild] and #childList > 0 then
            H4x.SelectedChild = childList[1]
        end
    end
end

childDropdown = TeleportTab:Dropdown({
    Title = "Select Lost Child",
    Values = {"Scanning for children..."},
    Value = "Scanning for children...",
    Multi = false,
    AllowNone = false,
    Callback = function(option)
        H4x.SelectedChild = option
    end
})

TeleportTab:Button({
    Title = "Teleport to Selected Child",
    Description = "Teleports to the selected lost child's location",
    Locked = false,
    Callback = function()
        if H4x.SelectedChild and H4x.SelectedChild ~= "No lost children found" and H4x.SelectedChild ~= "Scanning for children..." then
            local root = H4x.Player.Character and H4x.Player.Character:FindFirstChild("HumanoidRootPart")
            if root and childMap[H4x.SelectedChild] and childMap[H4x.SelectedChild].PrimaryPart then
                root.CFrame = CFrame.new(childMap[H4x.SelectedChild].PrimaryPart.Position + Vector3.new(0, 5, 0))
            end
        end
    end
})

startThread("DropdownUpdates", function()
    while true do
        updateChestDropdown()
        updateChildDropdown()
        task.wait(10) 
    end
end)

local function Feedback()
    loadstring(game:HttpGet("https://raw.githubusercontent.com/H4xScripts/Loader/refs/heads/main/feedback.lua", true))()
end

TeleportTab:Button({
    Title = "Feedback",
    Description = "You can use this to send feedback or request adding new features.",
    Callback = function()
        Feedback()
    end
})

PlayersTab:Section({
    Title = "Player Movement",
    TextXAlignment = "Left",
    TextSize = 17
})

PlayersTab:Toggle({
    Title = "Infinite Jump",
    Default = false,
    Callback = function(state)
        H4x.InfiniteJump = state
        if state then
            setupInfiniteJump()
        end
    end
})

PlayersTab:Slider({
    Title = "Walk Speed",
    Step = 1,
    Value = {
        Min = 16,
        Max = 100,
        Default = 16
    },
    Callback = function(value)
        H4x.WalkSpeedValue = value
        local character = H4x.Player.Character
        local humanoid = character and character:FindFirstChild("Humanoid")
        if humanoid then
            humanoid.WalkSpeed = value
        end
    end
})

PlayersTab:Slider({
    Title = "Jump Power",
    Step = 1,
    Value = {
        Min = 50,
        Max = 200,
        Default = 50
    },
    Callback = function(value)
        local character = H4x.Player.Character
        local humanoid = character and character:FindFirstChild("Humanoid")
        if humanoid then
            humanoid.JumpPower = value
        end
    end
})

PlayersTab:Toggle({
    Title = "No Clip",
    Default = false,
    Callback = function(state)
        H4x.NoClip = state
        updateNoClip(state)
    end
})

PlayersTab:Toggle({
    Title = "Fly",
    Default = false,
    Callback = function(state)
        H4x.Flying = state
        if state then
            setupFly()
        end
    end
})

PlayersTab:Slider({
    Title = "Fly Speed",
    Step = 0.1,
    Value = {
        Min = 10,
        Max = 200,
        Default = 50
    },
    Callback = function(value)
        H4x.FlySpeed = value
    end
})

H4x.Player.CharacterAdded:Connect(function(char)
    local humanoid = char:WaitForChild("Humanoid", 5)
    if humanoid then
        humanoid.WalkSpeed = H4x.WalkSpeedValue
    end
end)

game:BindToClose(function()
    cleanupConnections()
end)

startThread("CleanupThread", function()
    while true do
        task.wait(30) 
        collectgarbage("collect")
    end
end)

